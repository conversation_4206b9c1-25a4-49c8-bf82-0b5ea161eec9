package main

import (
	"bytes"
	"context"
	"encoding/binary"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/cilium/ebpf/link"
	"github.com/cilium/ebpf/ringbuf"
	"github.com/cilium/ebpf/rlimit"
	"golang.org/x/sys/unix"
)

//go:generate go run github.com/cilium/ebpf/cmd/bpf2go -cc clang -cflags "-O2 -g -Wall -Werror" -no-global-types schedlat bpf/schedlat.c

type SchedEvent struct {
	Pid        uint32
	Tgid       uint32
	Comm       [16]byte
	WakeupTime uint64
	SwitchTime uint64
	LatencyNs  uint64
}

// 全局偏移量：CLOCK_MONOTONIC → CLOCK_REALTIME
var monoToRealOffset time.Duration

func init() {
	var ts unix.Timespec
	if err := unix.ClockGettime(unix.CLOCK_MONOTONIC, &ts); err != nil {
		log.Fatalf("clock_gettime(CLOCK_MONOTONIC) failed: %v", err)
	}
	mono := time.Unix(0, ts.Nano())
	now := time.Now()
	monoToRealOffset = now.Sub(mono)
}

// 将内核 monotonic 时间戳转换为本地时间
func convertKernelTimeToLocal(kernelNs uint64) time.Time {
	monoTime := time.Unix(0, int64(kernelNs)) // event.SwitchTime 是 monotonic ns
	return monoTime.Add(monoToRealOffset)
}

func main() {
	// 移除内存限制
	if err := rlimit.RemoveMemlock(); err != nil {
		log.Fatal(err)
	}

	// 加载 BPF 对象
	spec, err := loadSchedlat()
	if err != nil {
		log.Fatal(err)
	}

	objs := schedlatObjects{}
	if err := spec.LoadAndAssign(&objs, nil); err != nil {
		log.Fatal(err)
	}
	defer objs.Close()

	// 附加 tracepoints
	wakeupLink, err := link.Tracepoint("sched", "sched_wakeup", objs.HandleSchedWakeup, nil)
	if err != nil {
		log.Fatal(err)
	}
	defer wakeupLink.Close()

	switchLink, err := link.Tracepoint("sched", "sched_switch", objs.HandleSchedSwitch, nil)
	if err != nil {
		log.Fatal(err)
	}
	defer switchLink.Close()

	fmt.Println("监控调度延迟... 按 Ctrl+C 退出")
	fmt.Printf("%-29s %-8s %-16s %-12s\n", "TIMESTAMP", "PID", "COMM", "LATENCY(us)")

	// 打开 ringbuf reader
	rd, err := ringbuf.NewReader(objs.Events)
	if err != nil {
		log.Fatal(err)
	}
	defer rd.Close()

	// 设置信号处理
	ctx, cancel := context.WithCancel(context.Background())
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)
	go func() {
		<-c
		cancel()
	}()

	// 读取事件
	go func() {
		for {
			record, err := rd.Read()
			if err != nil {
				if ctx.Err() != nil {
					return
				}
				log.Printf("读取 ringbuf 失败: %v", err)
				continue
			}

			var event SchedEvent
			if err := binary.Read(bytes.NewBuffer(record.RawSample), binary.LittleEndian, &event); err != nil {
				log.Printf("解析事件失败: %v", err)
				continue
			}

			comm := string(event.Comm[:bytes.IndexByte(event.Comm[:], 0)])
			latencyUs := float64(event.LatencyNs) / 1000.0

			// 转换时间戳
			eventTime := convertKernelTimeToLocal(event.SwitchTime)
			timeStr := eventTime.Format("2006-01-02 15:04:05.000000000")

			fmt.Printf("%-29s %-8d %-16s %-12.3f\n",
				timeStr, event.Pid, comm, latencyUs)
		}
	}()

	<-ctx.Done()
	fmt.Println("\n程序退出")
}
